#include "total.h"

volatile uint8_t buttonHandle_State;		//按键按下标志
volatile uint8_t buttonHandle_Record;		//按键按下状态记录
volatile uint32_t buttonHandle_Time;		//按键按下时间记录
volatile int g_iButton_State = 0;           //按键键码


/**
  * @brief  按键进程函数，提供按键消抖及按键事件指向
  * @param  无
  * @retval 无
  */
int button_Proc(void)
{
    static int button_num = 0;
    button_num = 0;
	if(buttonHandle_Record > 0)
	{
		if(Get_Tick() - buttonHandle_Time > 20)
		{
			switch(buttonHandle_Record){
				case 1:if(DL_GPIO_readPins(KEY_PORT,KEY_KEY1_PIN)==0)button_num = 1; button_Handle(1);break;
				case 2:if(DL_GPIO_readPins(KEY_PORT,KEY_KEY2_PIN)==0)button_num = 2; button_Handle(2);break;
                case 3:if(DL_GPIO_readPins(KEY_PORT,KEY_KEY3_PIN)==0)button_num = 3; button_Handle(3);break;
			}
			buttonHandle_Record = 0;
			buttonHandle_Time = 0;
		}
	}
    return button_num;
}


/**
  * @brief  按键事件处理函数，根据功能模式调整按键功能
  * @param  无
  * @retval 无
  */
void button_Handle(int key_num)
{
    if(Mode==11)
    {
        switch(key_num){
		case 1:
            // hAngle_PID.Setpoint = wz;
			break;
		case 2:
			break;
        case 3:
            break;
	    }
    }
}
	
