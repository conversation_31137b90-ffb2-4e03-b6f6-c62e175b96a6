#include "ti/driverlib/m0p/dl_core.h"
#include "total.h"          //头文件合集
#include "stdio.h"          

volatile int Mode = 0;
/*
    -功能模式说明-
    Mode = 0 ：待机模式，OLED打印各模块初始化及自检信息，LED快速闪烁，确认后进入菜单，
                        OLED显示菜单，OLED刷新、按键扫描频率快
    Mode = 1 ：第一题，小车朝正北放置在A点，按下按键，小车自动直线行驶至B点，蜂鸣器长鸣、LED长亮一秒
    Mode = 2 ：第二题，小车朝正北放置在A点，按下按键，小车自动直线行驶至B点，蜂鸣器短鸣、LED闪烁一次；
                        随后模拟量红外循弧线至C点，蜂鸣器短鸣、LED闪烁一次；随后自动直线行驶至D点，
                        蜂鸣器短鸣、LED闪烁一次；最后模拟量红外循弧线至C点，蜂鸣器长鸣、LED长亮一秒
    Mode = 3 ：第三题，小车朝正北放置在A点，按下按键，小车自动调整角度至东偏北50°并自动斜线行驶至C点，
                        蜂鸣器短鸣、LED闪烁一次；随后模拟量红外循线至B点，蜂鸣器短鸣、LED闪烁一次；
                        随后小车自动调整角度至西偏南50°并自动斜线行驶至D点，蜂鸣器短鸣、LED闪烁一次；
                        最后模拟量红外循迹至A点，蜂鸣器长鸣、LED长亮一秒
    Mode = 4 ：第四题，重复Mode4四圈并自动停车，蜂鸣器长鸣、LED快速闪烁
    Mode = 11：测速模式1，OLED显示PID与电机转速、按键扫描频率快，LED每800ms亮灭翻转
*/

volatile uint32_t current_time;                   //系统当前时间

volatile uint16_t TASK_OLED_DELAY = 200;          //OLED刷新频率
volatile uint16_t TASK_BUTTON_DELAY = 5;          //按键扫描频率


PalPID_HandleTypeDef hAngle_PID;             //角度环PID句柄声明
PalPID_HandleTypeDef hPosition_PID;          //位置环PID句柄声明



int main(void)
{
    
    SYSCFG_DL_init();       //芯片初始化

    DL_TimerG_startCounter(Motor_PWM_INST);             //电机PWM使能
    NVIC_EnableIRQ(TIMER_Encoder_Read_INST_INT_IRQN);   //编码器定时器使能
    NVIC_EnableIRQ(GPIO_MULTIPLE_GPIOA_INT_IRQN);       //GPIOA中断使能
    DL_Timer_startCounter(TIMER_Encoder_Read_INST);     //开启编码器定时器计时
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);         //清除UART0中断标志位
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);               //UART0中断使能
    NVIC_ClearPendingIRQ(UART_1_INST_INT_IRQN);         //清除UART1中断标志位
    NVIC_EnableIRQ(UART_1_INST_INT_IRQN);               //UART1中断使能
    // NVIC_EnableIRQ(ADC12_1_INST_INT_IRQN);              //ADC中断使能


    OLED_Init();                 //硬件I2C-OLED初始化
    OLED_Clear();                //OLED清屏
    control_PID_Init();          //PID控制器初始化
    motor_Direction_Set(0,0);    //电机初始化
    // tracking_Init();             //循迹初始化


    /*任务系统时间初始化*/
    current_time = Get_Tick();
    uint32_t last_task01_time = current_time;
	uint32_t last_task02_time = current_time;
    

    Mode = 0;   //test
    while(1)
    {
        printf("test\r\n");
        delay_cycles(80000000);
    }
    while (1) 
    {
        control_Proc();             //主控进程
        display_Remind_Proc();      //声光提示进程
        com_UART0Receive_Handle();  //UART0接收数据包成功标志位置位以后进行数据处理


        current_time = Get_Tick();
        /*按键进程*/
        if(current_time - last_task01_time >= TASK_BUTTON_DELAY)
        {
            // TODO Task01
            last_task01_time = current_time;
            button_Proc();
        }

        /*OLED进程*/
        if(current_time - last_task02_time >= TASK_OLED_DELAY)
        {
            // TODO Task02
            last_task02_time = current_time;
            display_OLED_Proc(display_OLED_mode);
        }

    }
}

