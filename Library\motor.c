#include "total.h"

/**
  * @brief  电机方向设置
  * @param  “motorA”：左轮，0-停止，1-启动  “motorB”：左轮，0-停止，1-启动
  * @retval 无
  */
void motor_Direction_Set(int motorA,int motorB)
{
    /*左电机*/
    if(motorA==0)
    {
        DL_GPIO_clearPins(MotorA_PORT,MotorA_AIN1_PIN);
        DL_GPIO_clearPins(MotorA_PORT,MotorA_AIN2_PIN);
    }
    else if(motorA==1)
    {
        DL_GPIO_setPins(MotorA_PORT,MotorA_AIN1_PIN);
        DL_GPIO_clearPins(MotorA_PORT,MotorA_AIN2_PIN);
    }
    /*右电机*/
    if(motorB==0)
    {
        DL_GPIO_clearPins(MotorB_PORT,MotorB_BIN1_PIN);
        DL_GPIO_clearPins(MotorB_PORT,MotorB_BIN2_PIN);
    }
    else if(motorB==1)
    {
        DL_GPIO_setPins(MotorB_PORT,MotorB_BIN1_PIN);
        DL_GPIO_clearPins(MotorB_PORT,MotorB_BIN2_PIN);
    }
}

void motor_Direction_Set_Auto(int PWMA,int PWMB)
{
    if(abs(PWMA)<50)
    {
        DL_GPIO_clearPins(MotorA_PORT,MotorA_AIN1_PIN);
        DL_GPIO_clearPins(MotorA_PORT,MotorA_AIN2_PIN);
    }
    else if(PWMA>0)
    {
        DL_GPIO_clearPins(MotorA_PORT,MotorA_AIN1_PIN);
        DL_GPIO_setPins(MotorA_PORT,MotorA_AIN2_PIN);
    }
    else if(PWMA<0)
    {
        DL_GPIO_setPins(MotorA_PORT,MotorA_AIN1_PIN);
        DL_GPIO_clearPins(MotorA_PORT,MotorA_AIN2_PIN);
    }

    if(abs(PWMB)<50)
    {
        DL_GPIO_clearPins(MotorB_PORT,MotorB_BIN1_PIN);
        DL_GPIO_clearPins(MotorB_PORT,MotorB_BIN2_PIN);
    }
    else if(PWMB>0)
    {
        DL_GPIO_setPins(MotorB_PORT,MotorB_BIN1_PIN);
        DL_GPIO_clearPins(MotorB_PORT,MotorB_BIN2_PIN);
    }
    else if(PWMB<0)
    {
        DL_GPIO_clearPins(MotorB_PORT,MotorB_BIN1_PIN);
        DL_GPIO_setPins(MotorB_PORT,MotorB_BIN2_PIN);
    }
}

/**
  * @brief  电机速度设置
  * @param  “PWMA”：左电机PWM，大小-2500-2500  “PWMB”:右电机PWM，大小-2500-2500
  * @retval 无
  */
void motor_Speed_Set(int PWMA,int PWMB)
{
    if(PWMA>2500)
    {
        PWMA = 2500;
    }
    else if(PWMA < -2500)
    {
        PWMA = -2500;
    }

    if(PWMB>2500)
    {
        PWMB = 2500;
    }
    else if(PWMB < -2500)
    {
        PWMB = -2500;
    }

    if(PWMA==0)
    {
        DL_TimerG_setCaptureCompareValue(Motor_PWM_INST, 0, DL_TIMER_CC_0_INDEX);
    }
    else if(PWMA>0)
    {
        DL_TimerG_setCaptureCompareValue(Motor_PWM_INST, PWMA, DL_TIMER_CC_0_INDEX);
    }
    else if(PWMA<0)
    {
        DL_TimerG_setCaptureCompareValue(Motor_PWM_INST, abs(PWMA), DL_TIMER_CC_0_INDEX);
    }
    
    if(PWMB==0)
    {
        DL_TimerG_setCaptureCompareValue(Motor_PWM_INST, 0, DL_TIMER_CC_1_INDEX);
    }
    else if(PWMB>0)
    {
        DL_TimerG_setCaptureCompareValue(Motor_PWM_INST, PWMB, DL_TIMER_CC_1_INDEX);
    }
    else if(PWMB<0)
    {
        DL_TimerG_setCaptureCompareValue(Motor_PWM_INST, abs(PWMB), DL_TIMER_CC_1_INDEX);
    }
}