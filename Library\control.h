#ifndef __CONTROL_H__
#define __CONTROL_H__

void control_PID_Init(void);
void control_Proc(void);
void control_Angle_PID(void);
void control_Position_PID(void);
void control_mode1(void);
void control_mode2(void);
void control_mode3(void);
void control_mode4(void);
void control_mode5(void);
void control_mode6(void);
void control_mode7(void);
void control_mode8(void);
void control_mode9(void);



#endif
