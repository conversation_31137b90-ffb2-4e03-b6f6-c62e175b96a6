#include "total.h"

volatile uint32_t gpioA;            //GPIOA中断状态
volatile uint64_t ticks = 0;        //系统节拍
volatile int32_t g_MotorA_Encoder_Count = 0, g_MotorB_Encoder_Count = 0;    //motorA和B编码器周期脉冲数
volatile float g_MotorA_Speed = 0, g_MotorB_Speed = 0;    //motorA和B实时速度,单位cm/s
volatile uint8_t Uart1_Receive_Data;    //UART1接收到的数据

/**
  * @brief  GROUP1中断服务函数，包含编码器外部中断、按键外部中断事件
  * @param  无
  * @retval 无
  */
void GROUP1_IRQHandler(void)
{
    
    gpioA = DL_GPIO_getEnabledInterruptStatus(GPIOA,GPIO_EncoderA_PIN_0_PIN | GPIO_EncoderA_PIN_1_PIN |
        GPIO_EncoderB_PIN_3_PIN | GPIO_EncoderB_PIN_4_PIN | KEY_KEY1_PIN | KEY_KEY2_PIN | KEY_KEY3_PIN);

    if((gpioA & GPIO_EncoderA_PIN_0_PIN) == GPIO_EncoderA_PIN_0_PIN)
    {
        //Pin0上升沿
        if(!DL_GPIO_readPins(GPIOA,GPIO_EncoderA_PIN_1_PIN))//P1为高电平
        {
            g_MotorA_Encoder_Count--;
        }
        else//P1为低电平
        {
            g_MotorA_Encoder_Count++;
        }
    }
    else if((gpioA & GPIO_EncoderA_PIN_1_PIN) == GPIO_EncoderA_PIN_1_PIN)
    {
        //Pin1上升沿
        if(!DL_GPIO_readPins(GPIOA,GPIO_EncoderA_PIN_0_PIN))//P0为高电平
        {
            g_MotorA_Encoder_Count++;
        }
        else//P1为低电平
        {
            g_MotorA_Encoder_Count--;
        }
    }
    else if((gpioA & GPIO_EncoderB_PIN_3_PIN) == GPIO_EncoderB_PIN_3_PIN)
    {
        //Pin0上升沿
        if(!DL_GPIO_readPins(GPIOA,GPIO_EncoderB_PIN_4_PIN))//P1为高电平
        {
            g_MotorB_Encoder_Count--;
        }
        else//P1为低电平
        {
            g_MotorB_Encoder_Count++;
        }
    }
    else if((gpioA & GPIO_EncoderB_PIN_4_PIN) == GPIO_EncoderB_PIN_4_PIN)
    {
        //Pin1上升沿
        if(!DL_GPIO_readPins(GPIOA,GPIO_EncoderB_PIN_3_PIN))//P0为高电平
        {
            g_MotorB_Encoder_Count++;
        }
        else//P1为低电平
        {
            g_MotorB_Encoder_Count--;
        }
    }
    else if((gpioA & KEY_KEY1_PIN) == KEY_KEY1_PIN)
    {
        buttonHandle_State = 1;
		buttonHandle_Record = buttonHandle_State;
		buttonHandle_Time = Get_Tick();
    }
    else if((gpioA & KEY_KEY2_PIN) == KEY_KEY2_PIN)
    {
        buttonHandle_State = 2;
		buttonHandle_Record = buttonHandle_State;
		buttonHandle_Time = Get_Tick();
    }
    else if((gpioA & KEY_KEY3_PIN) == KEY_KEY3_PIN)
    {
        buttonHandle_State = 3;
		buttonHandle_Record = buttonHandle_State;
		buttonHandle_Time = Get_Tick();
    }
    
    
    DL_GPIO_clearInterruptStatus(GPIOA,GPIO_EncoderA_PIN_0_PIN | GPIO_EncoderA_PIN_1_PIN |
        GPIO_EncoderB_PIN_3_PIN | GPIO_EncoderB_PIN_4_PIN | KEY_KEY1_PIN | KEY_KEY2_PIN | KEY_KEY3_PIN);
}


/**
  * @brief  20ms定时器中断服务函数，每20ms进行一次位置PID控制
  * @param  无
  * @retval 无
  */
void TIMER_Encoder_Read_INST_IRQHandler(void)
{
    switch (DL_TimerG_getPendingInterrupt(TIMER_Encoder_Read_INST)){
        case DL_TIMER_IIDX_ZERO:
            Position_PIDControl_Flag = true;
            break;
        default:
            break;
    }
}


// void ADC12_1_INST_IRQHandler(void)
// {
//     switch (DL_ADC12_getPendingInterrupt(ADC12_1_INST)) {
//         case DL_ADC12_IIDX_MEM4_RESULT_LOADED:
//             ADC_Flag = true;
//             break;
//         default:
//             break;
//     }
// }

/**
  * @brief  UART0接收中断服务函数，将接受到的数据传递给g_UART0_Receive_Data[64]数组
  * @param  无
  * @retval 无
  */
void UART_0_INST_IRQHandler(void)
{
    static int i = 0;
    switch (DL_UART_Main_getPendingInterrupt(UART_0_INST)) {
        case DL_UART_MAIN_IIDX_RX:
        //0xA9 | 0xA6 | 0xA5
            g_UART0_Receive_Data[i++] = DL_UART_Main_receiveData(UART_0_INST);
            if(g_UART0_Receive_Data[0] != 0x2C) i = 0;                  //帧头错误，数组清零
             if(i==7)
            {
                if(g_UART0_Receive_Data[6] == 0xC2)
                {
                    g_UART0_Receive_Data_Hex[0]=g_UART0_Receive_Data[2];
                    g_UART0_Receive_Data_Hex[1]=g_UART0_Receive_Data[3];
                    g_UART0_Receive_Data_Hex[2]=g_UART0_Receive_Data[4];
                    g_UART0_Receive_Data_Hex[3]=g_UART0_Receive_Data[5];
                    g_UART0_Receive_Data_Hex[4]=g_UART0_Receive_Data[1];
                    UART0_received = true;
                }
                i = 0;      //帧尾错误或处理完成，清空数组
            }
            
            break;
        default:
            break;
    }
}


/**
  * @brief  UART1接收中断服务函数
  * @param  无
  * @retval 无
  */
void UART_1_INST_IRQHandler(void)
{
    static int i = 0;
    switch (DL_UART_Main_getPendingInterrupt(UART_1_INST)) {
        case DL_UART_MAIN_IIDX_RX:
            Uart1_Receive_Data = DL_UART_Main_receiveData(UART_1_INST);
            JY901_Get_Angle();
            break;
        default:
            break;
    }
}


/**
  * @brief  滴答定时器中断服务函数，提供系统节拍时间，ticks每1ms加1
  * @param  无
  * @retval 无
  */
void SysTick_Handler(void)
{
    ticks++;
}


/**
  * @brief  获取系统节拍时间
  * @param  无
  * @retval 返回系统节拍时间ticks
  */
uint64_t Get_Tick(void)
{
    return ticks;
}