#include "total.h"

// volatile bool ADC_Flag = false;
// volatile uint16_t ADC_Val1, ADC_Val2, ADC_Val3, ADC_Val4, ADC_Val5;
// volatile uint16_t D1, D2, D3, D4, D5;
// volatile uint16_t Digital_Threshold = 2700;

// void sensor_AD_GetValue(void)
// {
//     ADC_Flag = false;
//     DL_ADC12_startConversion(ADC12_1_INST);
//     while(ADC_Flag == false);
//     ADC_Val1 = DL_ADC12_getMemResult(ADC12_1_INST, DL_ADC12_MEM_IDX_0);
//     ADC_Val2 = DL_ADC12_getMemResult(ADC12_1_INST, DL_ADC12_MEM_IDX_1);
//     ADC_Val3 = DL_ADC12_getMemResult(ADC12_1_INST, DL_ADC12_MEM_IDX_2);
//     ADC_Val4 = DL_ADC12_getMemResult(ADC12_1_INST, DL_ADC12_MEM_IDX_3);
//     ADC_Val5 = DL_ADC12_getMemResult(ADC12_1_INST, DL_ADC12_MEM_IDX_4);
//     DL_ADC12_enableConversions(ADC12_1_INST);
//     ADC_Flag = false;
// }

// void sensor_AD_GetDigital(void)
// {
//     ADC_Flag = false;
//     DL_ADC12_startConversion(ADC12_1_INST);
//     while(ADC_Flag == false);
//     ADC_Val1 = DL_ADC12_getMemResult(ADC12_1_INST, DL_ADC12_MEM_IDX_0);
//     ADC_Val2 = DL_ADC12_getMemResult(ADC12_1_INST, DL_ADC12_MEM_IDX_1);
//     ADC_Val3 = DL_ADC12_getMemResult(ADC12_1_INST, DL_ADC12_MEM_IDX_2);
//     ADC_Val4 = DL_ADC12_getMemResult(ADC12_1_INST, DL_ADC12_MEM_IDX_3);
//     ADC_Val5 = DL_ADC12_getMemResult(ADC12_1_INST, DL_ADC12_MEM_IDX_4);
    
//     if(ADC_Val1 > (B1_Max - sign))	D1 = 1;	else D1 = 0;
// 	if(ADC_Val2 > (B2_Max - sign))	D2 = 1;	else D2 = 0;
// 	if(ADC_Val3 > (B3_Max - sign))	D3 = 1;	else D3 = 0;
// 	if(ADC_Val4 > (B4_Max - sign))	D4 = 1;	else D4 = 0;
// 	if(ADC_Val5 > (B5_Max - sign))	D5 = 1;	else D5 = 0;
//     printf("%d %d %d %d %d\r\n",D1,D2,D3,D4,D5);
//     DL_ADC12_enableConversions(ADC12_1_INST);
//     ADC_Flag = false;
// }