#ifndef __PID_H__
#define __PID_H__

#include <stdint.h>

#define PAL_INVALID_TICK 0xffffffffffffffff


// PID初始化结构体定义  
typedef struct{  
    float Kp;         			// 比例增益  
    float Ki;        			// 积分增益  
    float Kd;         			// 微分增益  
    float Setpoint;   			// 设定点（目标值）  
    float OutputUpperLimit; 	// 输出上限  
    float OutputLowerLimit; 	// 输出下限  
    float DefaultOutput;    	// 默认输出值  
}PalPID_InitTypeDef;  
  
// PID句柄结构体定义，包含PID运行所需的所有状态  
typedef struct{  
    PalPID_InitTypeDef Init; 	// 初始化参数  
    uint64_t LastTime;          // 上次运行时间（微秒）  
    float ITerm;                // 积分项  
    float LastInput;            // 上次输入值  
    float LastError;            // 上次误差  
    float Kp;                   // 当前比例增益  
    float Ki;                   // 当前积分增益  
    float Kd;                   // 当前微分增益  
    float OutputUpperLimit;     // 当前输出上限  
    float OutputLowerLimit;     // 当前输出下限  
    float Setpoint;             // 当前设定点  
    bool Manual;                // 手动模式标志  
    float ManualOutput;         // 手动模式下的输出值  
}PalPID_HandleTypeDef; 

 void PAL_PID_Init(PalPID_HandleTypeDef *Handle);
 void PAL_PID_Cmd(PalPID_HandleTypeDef *Handle, uint8_t NewState);
 void PAL_PID_Reset(PalPID_HandleTypeDef *Handle);
float PAL_PID_Compute1(PalPID_HandleTypeDef *Handle, float Input);
float PAL_PID_Compute2(PalPID_HandleTypeDef *Handle, float Input, float dInput);
 void PAL_PID_ChangeTunings(PalPID_HandleTypeDef *Handle, float NewKp, float NewKi, float NewKd);
 void PAL_PID_GetTunings(PalPID_HandleTypeDef *Handle, float *pKpOut, float *pKiOut, float *pKdOut);
 void PAL_PID_ChangeSetpoint(PalPID_HandleTypeDef *Handle, float NewSetpoint);
float PAL_PID_GetSetpoint(PalPID_HandleTypeDef *Handle);
 void PAL_PID_ChangeManualOutput(PalPID_HandleTypeDef *Handle, float NewValue);

#endif
